<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="mb-4">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入搜索名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
            <el-option label="视频" :value="1" />
            <el-option label="图片" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据列表 -->
    <el-card>
      <template #header>
        <el-row>
          <el-col :span="12">
            <h5 class="card-title">数据集列表</h5>
          </el-col>
          <el-col :span="12" class="text-right">
            <el-button type="primary" @click="handleCreate">新建数据集</el-button>
          </el-col>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="datasetList">
        <el-table-column label="ID" align="center" prop="id" />
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="类型" align="center" prop="type">
          <template #default="scope">
            <el-tag :type="scope.row.type === 1 ? 'success' : 'primary'">
              {{ scope.row.type === 1 ? '视频' : '图片' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="大小" align="center" prop="size" />
        <el-table-column label="数量" align="center" prop="count" />
        <el-table-column label="用户" align="center" prop="creatorName" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ formatTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        class="mt-4"
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="getList"
        @current-change="getList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getDatasetPage, deleteDataset } from '@/api/alg/dataset'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatTime } from '@/utils/index'

defineOptions({ name: 'Dataset' })

const loading = ref(true)
const datasetList = ref([])
const total = ref(0)

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  type: undefined
})
const queryFormRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getDatasetPage(queryParams)
    datasetList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 新建按钮操作 */
const handleCreate = () => {
  // TODO: 跳转到新建页面或打开弹窗
  ElMessage.info('功能开发中...')
}

/** 编辑按钮操作 */
const handleUpdate = (row) => {
  // TODO: 跳转到编辑页面或打开弹窗
  ElMessage.info('功能开发中...')
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  await ElMessageBox.confirm(`确认删除数据集"${row.name}"?`, '提示', {
    type: 'warning'
  })
  await deleteDataset({ id: row.id })
  ElMessage.success('删除成功')
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.text-right {
  text-align: right;
}
</style>
