<template>
  <div class="dataset-card">
    <img class="card-image" :src="dataset.coverUrl || DEFAULT_COVER_URL" />
    <span class="card-title">{{ dataset.name }}</span>
    <div class="card-meta-row">
      <div class="meta-item">
        <span class="meta-label">类型：</span>
        <span class="meta-value">{{ getTypeLabel(dataset.type) }}</span>
      </div>
      <div class="meta-item">
        <span class="meta-label">大小：</span>
        <span class="meta-value">{{ dataset.size }} Mb</span>
      </div>
    </div>
    <div class="card-meta-row">
      <div class="meta-item">
        <span class="meta-label">数量：</span>
        <span class="meta-value">{{ dataset.count }}</span>
      </div>
      <div class="meta-item">
        <span class="meta-label">用户：</span>
        <span class="meta-value">{{ dataset.creatorName }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Dataset } from '../types'
import { DEFAULT_COVER_URL, DATASET_TYPE_LABELS } from '../constants'

defineProps<{
  dataset: Dataset
}>()

const getTypeLabel = (type: number) => {
  return DATASET_TYPE_LABELS[type] || '未知'
}
</script>

<style scoped>
.dataset-card {
  position: relative;
  width: 256px;
  height: 251px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.card-image {
  width: 100%;
  height: 160px;
  border-radius: 4px;
  object-fit: cover;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  color: #1f2329;
  white-space: pre;
  margin: 16px 0 12px;
}

.card-meta-row {
  display: flex;
  width: 100%;
}

.card-meta-row + .card-meta-row {
  margin-top: 12px;
}

.meta-item {
  flex: 0 0 50%;
  line-height: 16px;
  white-space: pre;
}

.meta-label,
.meta-value {
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
}

.meta-label {
  color: #8f959e;
}

.meta-value {
  color: #1f2329;
}
</style>
