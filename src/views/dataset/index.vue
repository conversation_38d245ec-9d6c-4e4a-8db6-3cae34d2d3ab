<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-4">
      <DatasetFilter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">数据集列表</h5>
        <el-button type="primary" @click="handleCreate">新建数据集</el-button>
      </div>
      <div class="grid-wrapper">
        <div class="card-grid" v-loading="loading">
          <DatasetCard
            v-for="item in datasetList"
            :key="item.id"
            :dataset="item"
            @update="handleUpdate(item)"
            @delete="handleDelete(item)"
          />
        </div>
      </div>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import DatasetCard from './components/DatasetCard.vue'
import DatasetFilter from './components/DatasetFilter.vue'
import { useDataset } from './useDataset'
import { PAGE_SIZES } from './constants'

defineOptions({ name: 'Dataset' })

const {
  loading,
  total,
  datasetList,
  queryParams,
  getList,
  handleQuery,
  handleUpdate,
  handleDelete
} = useDataset()

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.name = undefined
  queryParams.type = undefined
  handleQuery()
}

/** 新建按钮操作 */
const handleCreate = () => {
  // TODO: 跳转到新建页面或打开弹窗
  ElMessage.info('功能开发中...')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  height: calc(100vh - 242px);
  box-sizing: border-box;
}
.grid-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 20px;
}
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(256px, 1fr));
  gap: 20px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  height: 72px;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
